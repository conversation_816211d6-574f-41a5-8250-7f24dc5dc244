<template>
    <div class="grid">
      <div class="col-12">
        <div class="card">
          <div class="formgrid grid">
            <div class="field col-6 md:col-3">
              <label for="startDate">Start Date </label>
              <Calendar
                v-model="startDate"
                dateFormat="dd/mm/yy"
                inputId="startDate"
                :showIcon="true"
              />
            </div>
            <div class="field col-6 md:col-3">
              <label for="endDate">End Date </label>
              <Calendar
                v-model="endDate"
                dateFormat="dd/mm/yy"
                inputId="endDate"
                :showIcon="true"
              />
            </div>
            <div class="field col-6 md:col-3">
              <label for="clientEmail">Client Email (Optional)</label>
              <Dropdown
                id="clientEmail"
                v-model="selectedClient"
                :options="clients"
                optionLabel="email"
                placeholder="Select a client"
                :filter="true"
                :showClear="true"
              />
            </div>
            <div class="field col-6 md:col-3 flex align-items-end">
              <Button
                label="Generate Report"
                icon="pi pi-search"
                class="p-button-primary mr-2"
                @click="generateReport"
              />
              <Button
                label="Download CSV"
                icon="pi pi-download"
                class="p-button-success"
                @click="downloadCSV"
                :disabled="!reportData"
              />
            </div>
          </div>
  
          <TabView v-if="reportData">
            <TabPanel header="Summary">
              <!-- Overall Summary -->
              <div class="grid">
                <div class="col-12 md:col-6 lg:col-3">
                  <div class="card mb-0">
                    <div class="flex justify-content-between mb-3">
                      <div>
                        <span class="block text-500 font-medium mb-3">Total Clients</span>
                        <div class="text-900 font-medium text-xl">
                          {{ reportData.summary?.total_clients || 0 }}
                        </div>
                      </div>
                      <div class="flex align-items-center justify-content-center bg-blue-100 border-round" style="width: 2.5rem; height: 2.5rem">
                        <i class="pi pi-users text-blue-500 text-xl"></i>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-12 md:col-6 lg:col-3">
                  <div class="card mb-0">
                    <div class="flex justify-content-between mb-3">
                      <div>
                        <span class="block text-500 font-medium mb-3">Total Price ID Count</span>
                        <div class="text-900 font-medium text-xl">
                          {{ reportData.summary?.total_price_id_count || 0 }}
                        </div>
                      </div>
                      <div class="flex align-items-center justify-content-center bg-green-100 border-round" style="width: 2.5rem; height: 2.5rem">
                        <i class="pi pi-dollar text-green-500 text-xl"></i>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-12 md:col-6 lg:col-3">
                  <div class="card mb-0">
                    <div class="flex justify-content-between mb-3">
                      <div>
                        <span class="block text-500 font-medium mb-3">Racing Australia Usage</span>
                        <div class="text-900 font-medium text-xl">
                          {{ racingAustraliaUsage }}
                        </div>
                      </div>
                      <div class="flex align-items-center justify-content-center bg-orange-100 border-round" style="width: 2.5rem; height: 2.5rem">
                        <i class="pi pi-flag text-orange-500 text-xl"></i>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-12 md:col-6 lg:col-3">
                  <div class="card mb-0">
                    <div class="flex justify-content-between mb-3">
                      <div>
                        <span class="block text-500 font-medium mb-3">Mediality Racing Usage</span>
                        <div class="text-900 font-medium text-xl">
                          {{ medialityRacingUsage }}
                        </div>
                      </div>
                      <div class="flex align-items-center justify-content-center bg-purple-100 border-round" style="width: 2.5rem; height: 2.5rem">
                        <i class="pi pi-video text-purple-500 text-xl"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Client Summary Report -->
              <div class="card mt-4">
                <h5>Client Summary {{ formatDateRange() }}</h5>
                
                <div v-for="client in processedClientData" :key="client.clientEmail" class="mb-6">
                  <div class="grid align-items-center mb-3 p-3 surface-100 border-round">
                    <div class="col-12 md:col-3">
                      <div class="font-semibold">{{ client.clientName }}</div>
                      <div class="text-500 text-sm">{{ client.clientEmail }}</div>
                    </div>
                    <div class="col-12 md:col-3">
                      <div class="text-500 text-sm">Client Type</div>
                      <div>{{ client.clientType }}</div>
                    </div>
                    <div class="col-12 md:col-3">
                      <div class="text-500 text-sm">Racing Australia Tier</div>
                      <div>{{ client.racingAustraliaTier }}</div>
                    </div>
                    <div class="col-12 md:col-3">
                      <div class="text-500 text-sm">Mediality Racing Tier</div>
                      <div>{{ client.medialityRacingTier }}</div>
                    </div>
                  </div>

                  <!-- Racing Australia Data -->
                  <div v-if="client.racingAustraliaData.length > 0" class="mb-4">
                    <h6 class="text-orange-600 mb-3">
                      <i class="pi pi-flag mr-2"></i>Racing Australia Usage
                    </h6>
                    <DataTable
                      :value="client.racingAustraliaData"
                      class="p-datatable-sm"
                      tableStyle="min-width: 50rem"
                      :showGridlines="true"
                    >
                      <Column field="dataType" header="Data Type" style="width: 25%">
                        <template #body="slotProps">
                          <div class="font-medium">{{ slotProps.data.dataType }}</div>
                        </template>
                      </Column>
                      <Column header="Metro Sat/Sun" style="width: 15%" headerClass="text-center">
                        <template #body="slotProps">
                          <div class="text-center font-semibold text-blue-600">
                            {{ slotProps.data.metroSatSun || '' }}
                          </div>
                        </template>
                      </Column>
                      <Column header="Metro Midweek" style="width: 15%" headerClass="text-center">
                        <template #body="slotProps">
                          <div class="text-center font-semibold text-blue-600">
                            {{ slotProps.data.metroMidweek || '' }}
                          </div>
                        </template>
                      </Column>
                      <Column header="Provincial" style="width: 15%" headerClass="text-center">
                        <template #body="slotProps">
                          <div class="text-center font-semibold text-blue-600">
                            {{ slotProps.data.provincial || '' }}
                          </div>
                        </template>
                      </Column>
                      <Column header="Country" style="width: 15%" headerClass="text-center">
                        <template #body="slotProps">
                          <div class="text-center font-semibold text-blue-600">
                            {{ slotProps.data.country || '' }}
                          </div>
                        </template>
                      </Column>
                      <Column header="Total" style="width: 15%" headerClass="text-center">
                        <template #body="slotProps">
                          <div class="text-center font-bold text-green-600">
                            {{ slotProps.data.total || 0 }}
                          </div>
                        </template>
                      </Column>
                    </DataTable>
                  </div>

                  <!-- Mediality Racing Data -->
                  <div v-if="client.medialityRacingData.length > 0" class="mb-4">
                    <h6 class="text-purple-600 mb-3">
                      <i class="pi pi-video mr-2"></i>Mediality Racing Usage
                    </h6>
                    <DataTable
                      :value="client.medialityRacingData"
                      class="p-datatable-sm"
                      tableStyle="min-width: 50rem"
                      :showGridlines="true"
                    >
                      <Column field="dataType" header="Data Type" style="width: 40%">
                        <template #body="slotProps">
                          <div class="font-medium">{{ slotProps.data.dataType }}</div>
                        </template>
                      </Column>
                      <Column header="Usage Count" style="width: 30%" headerClass="text-center">
                        <template #body="slotProps">
                          <div class="text-center font-semibold text-purple-600">
                            {{ slotProps.data.total || 0 }}
                          </div>
                        </template>
                      </Column>
                      <Column header="Price IDs" style="width: 30%">
                        <template #body="slotProps">
                          <div class="flex flex-wrap gap-1">
                            <Chip 
                              v-for="priceId in slotProps.data.priceIds"
                              :key="priceId"
                              :label="priceId"
                              class="text-xs"
                              style="background-color: #e1d4f0; color: #6f42c1;"
                            />
                          </div>
                        </template>
                      </Column>
                    </DataTable>
                  </div>

                  <Divider v-if="client !== processedClientData[processedClientData.length - 1]" />
                </div>
              </div>
            </TabPanel>
  
            <TabPanel header="Details">
              <DataTable
                :value="reportData.details"
                :paginator="true"
                :rows="10"
                :rowsPerPageOptions="[5, 10, 25, 50]"
                tableStyle="min-width: 50rem"
              >
                <Column field="client_name" header="Client Name" sortable></Column>
                <Column field="contact_email" header="Email" sortable></Column>
                <Column field="price_id" header="Price ID" sortable>
                  <template #body="slotProps">
                    <Badge 
                      :value="slotProps.data.price_id"
                      :severity="slotProps.data.price_id.startsWith('R') ? 'warning' : slotProps.data.price_id.startsWith('M') ? 'info' : 'secondary'"
                    />
                  </template>
                </Column>
                <Column field="decoded_info" header="Decoded Info">
                  <template #body="slotProps">
                    <div class="text-sm">
                      <div><strong>Data Type:</strong> {{ decodePriceId(slotProps.data.price_id).dataType }}</div>
                      <div><strong>Location:</strong> {{ decodePriceId(slotProps.data.price_id).meetLocation }}</div>
                      <div v-if="decodePriceId(slotProps.data.price_id).category"><strong>Category:</strong> {{ decodePriceId(slotProps.data.price_id).category }}</div>
                    </div>
                  </template>
                </Column>
                <Column field="meeting_date" header="Meeting Date" sortable>
                  <template #body="slotProps">
                    {{ formatDate(slotProps.data.meeting_date) }}
                  </template>
                </Column>
                <Column field="count" header="Count" sortable></Column>
              </DataTable>
            </TabPanel>
          </TabView>
  
          <div v-if="!reportData && !loading" class="flex justify-content-center align-items-center p-5">
            <div class="text-center">
              <i class="pi pi-chart-bar text-primary" style="font-size: 3rem"></i>
              <h5>No Report Data</h5>
              <p>Select a date range and generate a report to view client usage data.</p>
            </div>
          </div>
  
          <div v-if="loading" class="flex justify-content-center align-items-center p-5">
            <ProgressSpinner style="width: 50px; height: 50px" strokeWidth="8" fill="var(--surface-ground)" animationDuration=".5s" />
          </div>
        </div>
      </div>
    </div>
  
    <Toast />
  </template>
  
  <script>
  import axios from "axios";
  import { API } from "aws-amplify";
  import Badge from "primevue/badge";
  import Button from "primevue/button";
  import Calendar from "primevue/calendar";
  import Chip from "primevue/chip";
  import Column from "primevue/column";
  import DataTable from "primevue/datatable";
  import Divider from "primevue/divider";
  import Dropdown from "primevue/dropdown";
  import ProgressSpinner from "primevue/progressspinner";
  import TabPanel from "primevue/tabpanel";
  import TabView from "primevue/tabview";
  import Toast from "primevue/toast";
  
  export default {
    components: {
      Badge,
      Button,
      Calendar,
      Chip,
      Column,
      DataTable,
      Divider,
      Dropdown,
      ProgressSpinner,
      TabPanel,
      TabView,
      Toast,
    },
    data() {
      return {
        startDate: this.getDefaultStartDate(),
        endDate: new Date(),
        selectedClient: null,
        clients: [],
        reportData: null,
        loading: false,
        priceIdMappings: {
          // Data Source Price Model
          dataSourceModel: {
            'RW': 'Racing Australia, Wholesale',
            'RR': 'Racing Australia, Retail',
            'MW': 'Mediality Racing, Wholesale',
            'MR': 'Mediality Racing, Retail'
          },
          // Data Type - Racing Australia
          racingAustraliaDataType: {
            'N': 'Fields @ Nominations',
            'W': 'Fields @ Weights', 
            'A': 'Fields @ Acceptances',
            'R': 'Results'
          },
          // Data Type - Mediality Racing
          medialityRacingDataType: {
            'C': 'Comments',
            'T': 'Ratings',
            'S': 'Jockey Silks',
            'F': 'Form'
          },
          // Customer Tier - Racing Australia
          racingAustraliaTier: {
            'A': 'Above $100mil',
            'B': '$50mil - $100mil',
            'C': '$25mil - $50mil',
            'D': '$10mil - $25mil',
            'E': '$2mil - $10mil',
            'F': 'Below $2mil',
            'G': 'Personal Use'
          },
          // Customer Tier - Mediality Racing
          medialityRacingTier: {
            '1': 'Tier 1',
            '2': 'Tier 2',
            'P': 'Personal Use',
            'W': 'Wholesale'
          },
          // Meet Location
          meetLocation: {
            'S': 'Metro Sat/Sun',
            'M': 'Metro Midweek',
            'P': 'Provincial',
            'C': 'Country'
          },
          // Category
          category: {
            'T': 'TAB',
            'N': 'Non-TAB'
          }
        }
      };
    },
    computed: {
      racingAustraliaUsage() {
        return this.reportData?.summary?.business_types?.racing_australia?.total_count || 0;
      },
      medialityRacingUsage() {
        return this.reportData?.summary?.business_types?.mediality_racing?.total_count || 0;
      },
      processedClientData() {
        if (!this.reportData || !this.reportData.client_summary) return [];
        
        return Object.entries(this.reportData.client_summary).map(([email, data]) => {
          const racingAustraliaData = this.processRacingAustraliaData(data.racing_australia.price_ids);
          const medialityRacingData = this.processMedialityRacingData(data.mediality_racing.price_ids);
          
          return {
            clientEmail: email,
            clientName: data.client_name,
            clientType: data.client_type,
            racingAustraliaTier: this.getTierDescription(data.racing_australia.pricing_tier, 'racing'),
            medialityRacingTier: this.getTierDescription(data.mediality_racing.pricing_tier, 'mediality'),
            racingAustraliaData,
            medialityRacingData
          };
        });
      }
    },
    mounted() {
      this.fetchClients();
    },
    methods: {
      getDefaultStartDate() {
        const d = new Date();
        d.setDate(d.getDate() - 30);
        return d;
      },
      isoDate(d) { 
        return d ? d.toISOString().split("T")[0] : ""; 
      },
      formatDate(d) {
        return d ? new Date(d).toLocaleDateString("en-AU", { day:"2-digit", month:"2-digit", year:"numeric" }) : "N/A";
      },
      formatDateRange() {
        return `${this.formatDate(this.startDate)} - ${this.formatDate(this.endDate)}`;
      },
      
      getTierDescription(tier, businessType) {
        if (businessType === 'racing') {
          return this.priceIdMappings.racingAustraliaTier[tier] || tier || 'WHOLESALE';
        } else {
          return this.priceIdMappings.medialityRacingTier[tier] || tier || 'WHOLESALE';
        }
      },
      
      decodePriceId(priceId) {
        if (!priceId) return { dataType: 'Unknown', meetLocation: 'Unknown', category: '' };
        
        const parts = priceId.split('-');
        if (parts.length < 4) return { dataType: 'Unknown', meetLocation: 'Unknown', category: '' };
        
        const [sourceModel, dataTypeCode, _tier, meetLocationCode, categoryCode] = parts;
        console.log(_tier);
        let dataType = 'Unknown';
        if (sourceModel.startsWith('R')) {
          dataType = this.priceIdMappings.racingAustraliaDataType[dataTypeCode] || dataTypeCode;
        } else if (sourceModel.startsWith('M')) {
          dataType = this.priceIdMappings.medialityRacingDataType[dataTypeCode] || dataTypeCode;
        }
        
        const meetLocation = this.priceIdMappings.meetLocation[meetLocationCode] || meetLocationCode;
        const category = categoryCode ? this.priceIdMappings.category[categoryCode] || categoryCode : '';
        
        return { dataType, meetLocation, category };
      },
      
      processRacingAustraliaData(priceIds) {
        const dataTypes = {};
        
        // Group by data type and category
        Object.entries(priceIds || {}).forEach(([priceId, count]) => {
          const decoded = this.decodePriceId(priceId);
          const parts = priceId.split('-');
          
          if (parts.length >= 5) {
            const [, _dataTypeCode, , meetLocationCode, categoryCode] = parts;
            const category = this.priceIdMappings.category[categoryCode] || '';
            const dataTypeKey = `${category} ${decoded.dataType}`.trim();
            console.log(_dataTypeCode);
            if (!dataTypes[dataTypeKey]) {
              dataTypes[dataTypeKey] = {
                dataType: dataTypeKey,
                metroSatSun: 0,
                metroMidweek: 0,
                provincial: 0,
                country: 0,
                total: 0
              };
            }
            
            // Map meet location to columns
            switch (meetLocationCode) {
              case 'S':
                dataTypes[dataTypeKey].metroSatSun += count;
                break;
              case 'M':
                dataTypes[dataTypeKey].metroMidweek += count;
                break;
              case 'P':
                dataTypes[dataTypeKey].provincial += count;
                break;
              case 'C':
                dataTypes[dataTypeKey].country += count;
                break;
            }
            dataTypes[dataTypeKey].total += count;
          }
        });
        
        return Object.values(dataTypes).filter(item => item.total > 0);
      },
      
      processMedialityRacingData(priceIds) {
        const dataTypes = {};
        
        Object.entries(priceIds || {}).forEach(([priceId, count]) => {
          const decoded = this.decodePriceId(priceId);
          
          if (!dataTypes[decoded.dataType]) {
            dataTypes[decoded.dataType] = {
              dataType: decoded.dataType,
              total: 0,
              priceIds: []
            };
          }
          
          dataTypes[decoded.dataType].total += count;
          dataTypes[decoded.dataType].priceIds.push(priceId);
        });
        
        return Object.values(dataTypes).filter(item => item.total > 0);
      },
  
      async fetchClients() {
        try {
          const response = await API.get("MrCenApiGateway", "/admin/client", {});
          this.clients = response.map((client) => ({
            id: client._id,
            name: client.account_holder,
            email: client.email,
            customerID: client.chargebee_customer_id,
          }));
        } catch (error) {
          console.error("Error fetching clients:", error);
          this.$toast.add({
            severity: "error",
            summary: "Error",
            detail: "Failed to fetch clients",
            life: 3000,
          });
        }
      },
      
      generateReport() {
        const body = {
          startDate: this.isoDate(this.startDate),
          endDate: this.isoDate(this.endDate),
          markAsRecorded: false
        };
        if (this.selectedClient) body.clientEmail = this.selectedClient.email;
        
        this.loading = true;
        axios
          .post(
            "https://lrxvklx2ib.execute-api.ap-southeast-2.amazonaws.com/report",
            body,
            { headers: { "Content-Type": "application/json" } }
          )
          .then(res => {
            this.reportData = res.data;
            this.$toast.add({
              severity: "success",
              summary: "Report Ready",
              detail: `Report ${this.formatDate(this.startDate)} – ${this.formatDate(this.endDate)} generated.`,
              life: 4000
            });
          })
          .catch(err => {
            console.error("Generate report failed:", err);
            this.$toast.add({
              severity: "error",
              summary: "Error",
              detail: "Could not generate the report. See console for details.",
              life: 5000
            });
          })
          .finally(() => {
            this.loading = false;
          });
      },
      
      downloadCSV() {
        if (!this.reportData) return;
        
        let csv = 'Client Summary Report\n';
        csv += `Date Range: ${this.formatDateRange()}\n\n`;
        
        this.processedClientData.forEach(client => {
          csv += `Client Name: ${client.clientName}\n`;
          csv += `Client Type: ${client.clientType}\n`;
          csv += `Racing Australia Tier: ${client.racingAustraliaTier}\n`;
          csv += `Mediality Racing Tier: ${client.medialityRacingTier}\n\n`;
          
          if (client.racingAustraliaData.length > 0) {
            csv += 'Racing Australia Usage:\n';
            csv += 'Data Type,Metro Sat/Sun,Metro Midweek,Provincial,Country,Total\n';
            client.racingAustraliaData.forEach(row => {
              csv += `"${row.dataType}",${row.metroSatSun || 0},${row.metroMidweek || 0},${row.provincial || 0},${row.country || 0},${row.total}\n`;
            });
            csv += '\n';
          }
          
          if (client.medialityRacingData.length > 0) {
            csv += 'Mediality Racing Usage:\n';
            csv += 'Data Type,Usage Count,Price IDs\n';
            client.medialityRacingData.forEach(row => {
              csv += `"${row.dataType}",${row.total},"${row.priceIds.join(', ')}"\n`;
            });
            csv += '\n';
          }
          
          csv += '---\n\n';
        });
        
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.setAttribute('href', url);
        link.setAttribute('download', `client-summary-report-${this.isoDate(this.startDate)}-to-${this.isoDate(this.endDate)}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        this.$toast.add({
          severity: "info",
          summary: "Download Started",
          detail: "Your CSV file is being downloaded",
          life: 3000,
        });
      }
    },
  };
  </script>
  
  <style scoped>
  .p-dropdown {
    width: 100%;
  }
  
  .p-datatable .p-datatable-thead > tr > th {
    background-color: var(--surface-200);
    font-weight: 600;
  }
  </style>